<!doctype html>
<html lang="cs">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>EduTech Day 2025</title>
  <meta name="description" content="<PERSON><PERSON><PERSON><PERSON><PERSON> (cca 150 znaků).">
  <link rel="icon" type="image/x-icon" href="/images/favicon.ico">

  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Mono:wght@100..900&display=swap" rel="stylesheet">

   <meta property="og:title" content="EduTech Day 2025">
   <meta property="og:description" content="Jednodenní konference o technologiích a vzdělávání – inspirace a inovace v praxi.">
   <meta property="og:image" content="https://example.com/og-image.jpg">
   <meta property="og:url" content="https://example.com/">
   <meta property="og:type" content="website">

   <meta name="twitter:card" content="summary_large_image">
   <meta name="twitter:title" content="EduTech Day 2025">
   <meta name="twitter:description" content="Jednodenní konference o technologiích a vzdělávání – inspirace a inovace v praxi.">
   <meta name="twitter:image" content="https://example.com/og-image.jpg">

   <style>
    * {
      box-sizing: border-box;
    }

    html {
      scroll-behavior: smooth;
      font-family: "Noto Sans Mono", monospace;
      color: white;
      background: #40444b;
      min-height: 100vh;
    }

    body {
      margin: 0;
      padding: 0;
      padding-top: 80px; /* Space for fixed header */
    }

    /* Liquid Glass Navbar */
    header {
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1000;
      width: 90%;
      max-width: 800px;
    }

    nav {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(2px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 15px 30px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: center;
      gap: 30px;
      transition: all 0.3s ease;
    }

    nav:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateY(-2px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    nav a {
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      font-weight: 500;
      padding: 10px 20px;
      border-radius: 12px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    nav a::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    nav a:hover::before {
      left: 100%;
    }

    nav a:hover {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      transform: translateY(-1px);
    }

    /* Back to top button */
    #btt {
      position: fixed;
      right: 20px;
      bottom: 20px;
      padding: 15px;
      font-size: 24px;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      color: white;
      text-decoration: none;
      transition: all 0.3s ease;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    #btt:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    /* Main content styling */
    main {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }

    h1 {
      text-align: center;
      font-size: 3rem;
      margin-bottom: 10px;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    h2 {
      font-size: 2rem;
      margin: 40px 0 20px 0;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* Speakers section */
    .speakers {
      display: flex;
      justify-content: center;
      gap: 30px;
      flex-wrap: wrap;
      margin: 30px 0;
    }

    .speakers figure {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;
      max-width: 300px;
    }

    .speakers figure:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .speakers img {
      border-radius: 15px;
      transition: transform 0.3s ease;
    }

    .speakers figure:hover img {
      transform: scale(1.05);
    }

    /* Table styling */
    table {
      width: 100%;
      border-collapse: collapse;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 15px;
      overflow: hidden;
      margin: 20px 0;
    }

    th, td {
      padding: 15px;
      text-align: left;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    th {
      background: rgba(255, 255, 255, 0.1);
      font-weight: 600;
    }

    /* Form styling */
    .map-form {
      display: flex;
      justify-content: center;
      gap: 30px;
      flex-wrap: wrap;
      margin: 30px 0;
    }

    form {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 30px;
      max-width: 500px;
    }

    fieldset {
      border: none;
      padding: 0;
    }

    legend {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin: 15px 0 5px 0;
      font-weight: 500;
    }

    input[type="text"], input[type="email"], input[type="tel"] {
      width: 100%;
      padding: 12px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 10px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-family: inherit;
    }

    input[type="text"]::placeholder,
    input[type="email"]::placeholder,
    input[type="tel"]::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }

    input[type="submit"] {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 12px 30px;
      border-radius: 10px;
      cursor: pointer;
      font-family: inherit;
      font-weight: 600;
      transition: all 0.3s ease;
      margin-top: 20px;
    }

    input[type="submit"]:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    /* FAQ styling */
    details {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 15px;
      margin: 15px 0;
      padding: 20px;
    }

    summary {
      cursor: pointer;
      font-weight: 600;
      padding: 10px 0;
    }

    /* Footer */
    footer {
      text-align: center;
      padding: 40px 20px;
      background: rgba(0, 0, 0, 0.2);
      margin-top: 60px;
    }

    /* Map container */
    .map-container {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 20px;
      padding: 20px;
      max-width: 600px;
    }

    iframe {
      border-radius: 15px;
      width: 100%;
    }

    address {
      margin-top: 15px;
      font-style: normal;
      text-align: center;
    }

   </style>

</head>
<body>
  <header>
    <nav aria-label="Hlavní navigace">
      <a href="#program">Program akce</a>
      <a href="#speakers">Speakeři</a>
      <a href="#venuetickets">Lístky a místo konání</a>
    </nav>
  </header>

  <a href="#main" id="btt" aria-label="Zpět nahoru">
    <svg width="24" height="24" viewBox="0 0 32 32" fill="currentColor" aria-hidden="true">
      <path d="m26.71 10.29-10-10a1 1 0 0 0-1.41 0l-10 10 1.41 1.41L15 3.41V32h2V3.41l8.29 8.29z"/>
    </svg>
  </a>


  <main id="main">
    <section>
      <h1>EduTech Day 2025</h1>
      <p>Inovace, technologie, inspirace.</p>
      <p>Datum konání: <time datetime="2025-03-15">15. března 2025</time></p>
    </section>

    <section id="program">
      <h2>Program akce</h2>
      <table>
        <caption>Harmonogram EduTech Day 2025</caption>
        <thead>
          <tr>
            <th scope="col">Segment</th>
            <th scope="col">Doba konání</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Seminář metodik učení</td>
            <td><time datetime="10:00">10:00</time> - <time datetime="11:00">11:00</time></td>
          </tr>
          <tr>
            <td>Seminář <cite>Metra Páry</cite></td>
            <td><time datetime="11:30">11:30</time> - <time datetime="13:00">13:00</time></td>
          </tr>
          <tr>
            <td>Workshop: <abbr title="Artificial Intelligence">AI</abbr> ve výuce</td>
            <td><time datetime="13:15">13:15</time> - <time datetime="14:00">14:00</time></td>
          </tr>
          <tr>
            <td>Přednáška o budoucnosti vzdělávání</td>
            <td><time datetime="14:10">14:10</time> - <time datetime="14:45">14:45</time></td>
          </tr>
          <tr>
            <td>Workshop: Gamifikace vzdělání</td>
            <td><time datetime="15:00">15:00</time> - <time datetime="16:00">16:00</time></td>
          </tr>
        </tbody>
      </table>
    </section>



    <section id="speakers">
      <h2>Speakeři</h2>
      <div class="speakers">
        <article>
          <figure>
            <img src="https://yt3.googleusercontent.com/Nma7R7np_tbNw11xywygKd9AW0TJKSHbEsQNoVwGk1g1uwhsuZF5OKaZcsbREi3dVpFqFcZm=s900-c-k-c0x00ffffff-no-rj"
                 width="400" height="400" alt="Portrét Petra Máry">
            <figcaption>Petr Mára</figcaption>
          </figure>
          <p>Expert na technologie a vzdělávací metodiky</p>
          <p>Hodnocení: <
        </article>

        <article>
          <figure>
            <img src="https://upload.wikimedia.org/wikipedia/commons/0/01/LinuxCon_Europe_Linus_Torvalds_03_%28cropped%29.jpg"
                 width="400" height="400" alt="Portrét Linuse Torvaldse">
            <figcaption>Linus Torvalds</figcaption>
          </figure>
          <p>Tvůrce operačního systému Linux</p>
          <p>Hodnocení: <data value="5.0">5.0/5</data> ⭐</p>
        </article>

        <article>
          <figure>
            <img src="https://i.pinimg.com/474x/a7/fc/53/a7fc53d83a6025438f96067f1fac7664.jpg"
                 width="400" height="400" alt="Portrét Marka Zuckerberga">
            <figcaption>Mark Zuckerberg</figcaption>
          </figure>
          <p>Zakladatel a <abbr title="Chief Executive Officer">CEO</abbr> Meta (Facebook)</p>
          <p>Hodnocení: <data value="3.2">3.2/5</data> ⭐</p>
        </article>
      </div>
    </section>

    <section id="venuetickets">
      <h2>Místo konání &amp; vstupenky</h2>
      <div class="map-form">
        <div class="map-container">
          <iframe width="100%" height="400"
                  src="https://maps.google.com/maps?width=100%25&amp;height=800&amp;hl=cs&amp;q=48.7718361%2018.6234916%20+(EduTech%20Day%202025)&amp;t=&amp;z=14&amp;ie=UTF8&amp;iwloc=B&amp;output=embed"
                  title="Mapa místa konání EduTech Day 2025">
          </iframe>
          <address>
            Námestie slobody 811 06<br>
            Bratislava, Slovensko
          </address>
        </div>

        <form action="/submit" method="post">
          <fieldset>
            <legend>Registrace na EduTech Day 2025</legend>

            <label for="name">Jméno:</label>
            <input type="text" id="name" name="name" placeholder="Jan Novák" required>

            <label for="email">E-mail:</label>
            <input type="email" id="email" name="email" placeholder="<EMAIL>" required>

            <label for="phone">Telefon:</label>
            <input type="tel" id="phone" name="phone" placeholder="+420 123 456 789"
                   pattern="^\+420\s\d{3}\s\d{3}\s\d{3}$"
                   aria-describedby="phoneHelp" required>
            <small id="phoneHelp">Formát: +420 123 456 789</small>

            <input type="submit" value="Registrovat se">
          </fieldset>

          <noscript>
            <p>Formulář funguje i bez JavaScriptu, ale některé funkce nemusí být aktivní.</p>
          </noscript>
        </form>
      </div>
    </section>


    <section>
      <h2>FAQ</h2>
      <details>
        <summary>Jak se mohu registrovat na konferenci?</summary>
        <p>Registrace probíhá přes náš online formulář na této stránce v sekci "Místo konání &amp; vstupenky".</p>
      </details>

      <details>
        <summary>Mohu vstoupit s jídlem a pitím?</summary>
        <p>Do sálu není povolen vstup s jídlem, ale občerstvení je k dispozici v přilehlé kavárně.</p>
      </details>

      <details>
        <summary>Jak je to s bezpečností na Slovensku?</summary>
        <img src="https://i.ytimg.com/vi/i9WpYaVdiz0/oardefault.jpg?sqp=-oaymwEYCJUDENAFSFqQAgHyq4qpAwcIARUAAIhC&rs=AOn4CLBwYCEn7ZKDvvwuImzOHxcKUiSr3w"
             alt="Informační video o bezpečnosti na Slovensku" width="480" height="360">
      </details>

      <details>
        <summary>Jaké jsou možnosti dopravy?</summary>
        <p>Místo konání je dostupné <abbr title="městskou hromadnou dopravou">MHD</abbr>, autem i pěšky z centra Bratislavy.</p>
      </details>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 EduTech Day. Všechna práva vyhrazena.</p>
  </footer>

  <!-- Ostatní skripty až před </body> s defer (neblokují DOM) -->
  <!-- <script defer src="https://widgets.example.com/edutech.js"></script> -->
</body>
</html>